{"name": "crawlguard/crawlguard-wp", "description": "WordPress plugin for AI content monetization and bot detection", "type": "wordpress-plugin", "license": "GPL-2.0-or-later", "authors": [{"name": "CrawlGuard Team", "email": "<EMAIL>"}], "require": {"php": ">=7.4", "composer/installers": "^1.0"}, "require-dev": {"phpunit/phpunit": "^9.0", "squizlabs/php_codesniffer": "^3.6", "wp-coding-standards/wpcs": "^2.3", "phpcompatibility/phpcompatibility-wp": "^2.1", "dealerdirect/phpcodesniffer-composer-installer": "^0.7"}, "scripts": {"phpcs": "phpcs --standard=WordPress --extensions=php --ignore=*/vendor/*,*/node_modules/* .", "phpcbf": "phpcbf --standard=WordPress --extensions=php --ignore=*/vendor/*,*/node_modules/* .", "test": "phpunit", "security-scan": "phpcs --standard=Security --extensions=php --ignore=*/vendor/*,*/node_modules/* .", "post-install-cmd": ["\"vendor/bin/phpcs\" --config-set installed_paths vendor/wp-coding-standards/wpcs"]}, "config": {"allow-plugins": {"composer/installers": true, "dealerdirect/phpcodesniffer-composer-installer": true}}, "autoload": {"psr-4": {"CrawlGuard\\": "includes/"}}, "autoload-dev": {"psr-4": {"CrawlGuard\\Tests\\": "tests/"}}}