<?php
/**
 * Admin Interface for CrawlGuard WP
 */

if (!defined('ABSPATH')) {
    exit;
}

class CrawlGuard_Admin {

    public function __construct() {
        // Hook into WordPress admin
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'init_settings'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_crawlguard_get_analytics', array($this, 'ajax_get_analytics'));

        // Add admin notices for debugging
        add_action('admin_notices', array($this, 'admin_notices'));
    }
    
    public function add_admin_menu() {
        add_menu_page(
            'CrawlGuard WP',
            'CrawlGuard',
            'manage_options',
            'crawlguard',
            array($this, 'admin_page'),
            'dashicons-shield-alt',
            30
        );
        
        add_submenu_page(
            'crawlguard',
            'Dashboard',
            'Dashboard',
            'manage_options',
            'crawlguard',
            array($this, 'admin_page')
        );
        
        add_submenu_page(
            'crawlguard',
            'Settings',
            'Settings',
            'manage_options',
            'crawlguard-settings',
            array($this, 'settings_page')
        );
    }
    
    public function admin_page() {
        $options = get_option('crawlguard_options', array());
        $api_key = $options['api_key'] ?? '';
        $is_configured = !empty($api_key);
        ?>
        <div class="wrap">
            <h1>CrawlGuard WP Dashboard</h1>

            <?php if (!$is_configured): ?>
                <div class="notice notice-warning">
                    <p><strong>CrawlGuard Setup Required:</strong> Please configure your API key in the <a href="<?php echo admin_url('admin.php?page=crawlguard-settings'); ?>">Settings</a> page to start protecting your site.</p>
                </div>
            <?php else: ?>
                <div class="notice notice-success">
                    <p><strong>CrawlGuard Active:</strong> Your site is protected with API key: <code><?php echo esc_html(substr($api_key, 0, 20) . '...'); ?></code></p>
                </div>
            <?php endif; ?>

            <div id="crawlguard-dashboard">
                <div class="card">
                    <h2>Bot Protection Status</h2>
                    <p><?php echo $is_configured ? 'Active and monitoring bot traffic' : 'Not configured - please add your API key'; ?></p>
                </div>

                <div class="card">
                    <h2>Quick Setup</h2>
                    <ol>
                        <li>Get your API key from the AI Crawler Guard dashboard</li>
                        <li>Go to <a href="<?php echo admin_url('admin.php?page=crawlguard-settings'); ?>">CrawlGuard Settings</a></li>
                        <li>Paste your API key and save</li>
                        <li>Enable monetization if desired</li>
                    </ol>
                </div>
            </div>
        </div>
        <?php
    }
    
    public function settings_page() {
        $options = get_option('crawlguard_options', array());
        $api_key = $options['api_key'] ?? '';
        ?>
        <div class="wrap">
            <h1>CrawlGuard Settings</h1>

            <div class="card">
                <h2>API Configuration</h2>
                <p>Configure your CrawlGuard API settings to start protecting your WordPress site from AI bots.</p>

                <form method="post" action="options.php">
                    <?php
                    settings_fields('crawlguard_settings');
                    do_settings_sections('crawlguard_settings');
                    submit_button('Save Settings');
                    ?>
                </form>
            </div>

            <?php if (!empty($api_key)): ?>
            <div class="card">
                <h2>Connection Status</h2>
                <p><span class="dashicons dashicons-yes-alt" style="color: green;"></span> API Key configured</p>
                <p><strong>Current API Key:</strong> <code><?php echo esc_html(substr($api_key, 0, 20) . '...'); ?></code></p>
            </div>
            <?php endif; ?>

            <div class="card">
                <h2>How to Get Your API Key</h2>
                <ol>
                    <li>Log in to your AI Crawler Guard dashboard</li>
                    <li>Go to the WordPress Sites section</li>
                    <li>Find your site and copy the API key</li>
                    <li>Paste it in the API Key field above</li>
                </ol>
                <p><strong>Example API Key format:</strong> <code>cg_e1b90f892dd106b9feaf5bc98d0457c7</code></p>
            </div>
        </div>
        <?php
    }
    
    public function init_settings() {
        register_setting('crawlguard_settings', 'crawlguard_options');
        
        add_settings_section(
            'crawlguard_main_section',
            'Main Settings',
            array($this, 'main_section_callback'),
            'crawlguard_settings'
        );
        
        add_settings_field(
            'api_key',
            'API Key',
            array($this, 'api_key_callback'),
            'crawlguard_settings',
            'crawlguard_main_section'
        );
        
        add_settings_field(
            'monetization_enabled',
            'Enable Monetization',
            array($this, 'monetization_enabled_callback'),
            'crawlguard_settings',
            'crawlguard_main_section'
        );
    }
    
    public function main_section_callback() {
        echo '<p>Enter your API key from the AI Crawler Guard dashboard to connect your WordPress site.</p>';
    }

    public function api_key_callback() {
        $options = get_option('crawlguard_options', array());
        $api_key = $options['api_key'] ?? '';
        ?>
        <input type="text"
               name="crawlguard_options[api_key]"
               value="<?php echo esc_attr($api_key); ?>"
               class="regular-text"
               placeholder="cg_e1b90f892dd106b9feaf5bc98d0457c7"
               style="width: 400px;" />
        <p class="description">
            Paste your API key from the AI Crawler Guard dashboard. It should start with "cg_" followed by 32 characters.
        </p>
        <?php
    }
    
    public function monetization_enabled_callback() {
        $options = get_option('crawlguard_options');
        $enabled = $options['monetization_enabled'] ?? false;
        echo '<input type="checkbox" name="crawlguard_options[monetization_enabled]" value="1" ' . checked(1, $enabled, false) . ' />';
    }
    
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'crawlguard') === false) {
            return;
        }
        
        wp_enqueue_script(
            'crawlguard-admin',
            CRAWLGUARD_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            CRAWLGUARD_VERSION,
            true
        );
        
        wp_localize_script('crawlguard-admin', 'crawlguard_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('crawlguard_nonce')
        ));
    }
    
    public function ajax_get_analytics() {
        check_ajax_referer('crawlguard_nonce', 'nonce');

        if (class_exists('CrawlGuard_API_Client')) {
            $api_client = new CrawlGuard_API_Client();
            $analytics = $api_client->get_analytics();
            wp_send_json_success($analytics);
        } else {
            wp_send_json_error('API Client not available');
        }
    }

    public function admin_notices() {
        // Only show on CrawlGuard pages
        $screen = get_current_screen();
        if (!$screen || strpos($screen->id, 'crawlguard') === false) {
            return;
        }

        $options = get_option('crawlguard_options', array());
        $api_key = $options['api_key'] ?? '';

        // Show setup notice if no API key
        if (empty($api_key)) {
            ?>
            <div class="notice notice-info is-dismissible">
                <p><strong>CrawlGuard Setup:</strong> Welcome! Please configure your API key in the settings below to start protecting your site from AI bots.</p>
            </div>
            <?php
        }
    }
}
